import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetModal } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import { VendorDetails } from "@/types/vendor";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import {
  Title,
  SearchContainer,
  SearchInput,
  ListContainer,
  VendorItem,
  VendorInfo,
  VendorName,
  VendorId,
  EmptyContainer,
  EmptyText,
  StyledBottomSheetView,
} from "./styles";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";

interface VendorSelectionBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (vendor: VendorDetails) => void;
  vendors: VendorDetails[];
  isLoading?: boolean;
}

const VendorSelectionBottomSheet: React.FC<VendorSelectionBottomSheetProps> = ({
  isVisible,
  onClose,
  onSelect,
  vendors,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const sheetRef = useRef<BottomSheetModal>(null);
  const [query, setQuery] = useState("");
  const [filteredVendors, setFilteredVendors] =
    useState<VendorDetails[]>(vendors);

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  useEffect(() => {
    if (!query) {
      setFilteredVendors(vendors);
      return;
    }

    setFilteredVendors(
      vendors.filter((vendor) => {
        const fullName =
          `${vendor.first_name} ${vendor.last_name}`.toLowerCase();
        const searchQuery = query.toLowerCase();
        return (
          fullName.includes(searchQuery) ||
          vendor.vendor_id?.toString().includes(searchQuery) ||
          vendor.email?.toLowerCase().includes(searchQuery)
        );
      })
    );
  }, [query, vendors]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["70%"], []);

  const handleSearch = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const handleVendorSelect = useCallback(
    (vendor: VendorDetails) => {
      onSelect(vendor);
      onClose();
    },
    [onSelect, onClose]
  );

  const renderVendorItem = useCallback(
    ({ item }: { item: VendorDetails }) => (
      <VendorItem onPress={() => handleVendorSelect(item)}>
        <VendorInfo>
          <VendorName>{item.first_name + " " + item.last_name}</VendorName>
          {item.email && <VendorId>{item.email}</VendorId>}
        </VendorInfo>
      </VendorItem>
    ),
    [handleVendorSelect]
  );

  const renderEmptyComponent = useCallback(() => {
    if (isLoading) {
      return <LoadingOverlay isLoading={true} size="large" />;
    }

    return (
      <EmptyContainer>
        <EmptyText>
          {query
            ? t("common.no_results_found")
            : t("common.no_vendors_available")}
        </EmptyText>
      </EmptyContainer>
    );
  }, [isLoading, query, t]);

  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      style={{ borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      backdropComponent={renderBackdrop}
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      enablePanDownToClose
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView>
        <Title>{t("cart.selectVendor")}</Title>

        <SearchContainer>
          <Ionicons name="search" size={20} color={theme.colors.text} />
          <SearchInput
            placeholder={t("cart.searchVendor")}
            value={query}
            onChangeText={handleSearch}
            placeholderTextColor={theme.colors.text}
          />
        </SearchContainer>

        <ListContainer
          data={filteredVendors}
          keyExtractor={(item) => item.id?.toString() || item.mobile.toString()}
          renderItem={renderVendorItem}
          ListEmptyComponent={renderEmptyComponent}
          showsVerticalScrollIndicator={false}
        />
      </StyledBottomSheetView>
    </BottomSheetModal>
  );
};

export default VendorSelectionBottomSheet;

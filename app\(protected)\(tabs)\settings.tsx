import { Switch, Pressable } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useState } from "react";
import { logoutAction } from "@/store/actions/auth";
import { useTranslation } from "react-i18next";
import { Language } from "@/store/slices/settingsSlice";
import { useAppDispatch, useAppSelector } from "@/store/store";
import Toast from "react-native-toast-message";
import { useTheme } from "@/hooks/useTheme";
import { ConfirmationModal } from "@/components/molecules";
import { UserType } from "@/types/api";
import {
  Container,
  Content,
  Section,
  SectionTitle,
  SettingItem,
  SettingText,
  CurrentLanguage,
  ModalOverlay,
  ModalContent,
  ModalInnerContent,
  ModalTitle,
  LanguageOption,
  LanguageText,
  CloseButton,
  CloseButtonText,
} from "@/styles/Settings.styles";

const LANGUAGES = [
  { code: Language.EN, name: "english" },
  { code: Language.HI, name: "hindi" },
  { code: Language.GU, name: "gujarati" },
];

export default function SettingsScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isSalesPerson = user?.role_id === UserType.SALESPERSON;
  const [notifications, setNotifications] = useState({
    push: true,
    email: true,
    whatsapp: false,
  });
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = () => {
    setShowLogoutModal(true);
  };

  const confirmLogout = async () => {
    try {
      setIsLoggingOut(true);
      const response = await dispatch(logoutAction({})).unwrap();
      if (response.status) {
        Toast.show({
          type: "success",
          text1: response.message,
        });
        router.replace("/");
      } else {
        Toast.show({
          type: "error",
          text1: response.message,
        });
      }
    } catch (error) {
      setIsLoggingOut(false);
      Toast.show({
        type: "error",
        text1: t("logout.error"),
      });
    }
  };

  const toggleNotification = (type: keyof typeof notifications) => {
    setNotifications((prev) => ({
      ...prev,
      [type]: !prev[type],
    }));
  };

  const changeLanguage = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
    setShowLanguageModal(false);
    Toast.show({
      type: "success",
      text1: t("settings.languageChangeSuccess"),
    });
  };

  return (
    <Container>
      <Content showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <Section>
          <SectionTitle>{t("profile.title")}</SectionTitle>
          <SettingItem onPress={() => router.push("/profile")}>
            <Ionicons
              name="person-outline"
              size={24}
              color={theme.colors.icon}
            />
            <SettingText>{t("profile.title")}</SettingText>
            <Ionicons
              name="chevron-forward"
              size={24}
              color={theme.colors.iconSecondary}
            />
          </SettingItem>
          <SettingItem onPress={() => router.push("/profile/edit")}>
            <Ionicons
              name="create-outline"
              size={24}
              color={theme.colors.icon}
            />
            <SettingText>{t("profile.editProfile")}</SettingText>
            <Ionicons
              name="chevron-forward"
              size={24}
              color={theme.colors.iconSecondary}
            />
          </SettingItem>
        </Section>

        {/* Vendor Management Section - Only for Salesperson */}
        {isSalesPerson && (
          <Section>
            <SectionTitle>{t("vendorManagement.title")}</SectionTitle>
            <SettingItem onPress={() => router.push("/vendor-management")}>
              <Ionicons
                name="people-outline"
                size={24}
                color={theme.colors.icon}
              />
              <SettingText>{t("vendorManagement.manageVendors")}</SettingText>
              <Ionicons
                name="chevron-forward"
                size={24}
                color={theme.colors.iconSecondary}
              />
            </SettingItem>
          </Section>
        )}

        {/* Language Section */}
        <Section>
          <SectionTitle>{t("language")}</SectionTitle>
          <SettingItem onPress={() => setShowLanguageModal(true)}>
            <Ionicons
              name="language-outline"
              size={24}
              color={theme.colors.icon}
            />
            <SettingText>{t("language_selection")}</SettingText>
            <CurrentLanguage>
              {t(
                LANGUAGES.find((lang) => lang.code === i18n.language)?.name ||
                  "english"
              )}
            </CurrentLanguage>
            <Ionicons
              name="chevron-forward"
              size={24}
              color={theme.colors.iconSecondary}
            />
          </SettingItem>
        </Section>

        {/* Notifications Section */}
        <Section>
          <SectionTitle>{t("notifications")}</SectionTitle>
          <SettingItem>
            <Ionicons
              name="notifications-outline"
              size={24}
              color={theme.colors.icon}
            />
            <SettingText>{t("push_notifications")}</SettingText>
            <Switch
              value={notifications.push}
              onValueChange={() => toggleNotification("push")}
              trackColor={{
                false: theme.colors.switchTrack,
                true: theme.colors.switchTrackActive,
              }}
              thumbColor={
                notifications.push
                  ? theme.colors.switchThumbActive
                  : theme.colors.switchThumb
              }
            />
          </SettingItem>
          <SettingItem>
            <Ionicons name="mail-outline" size={24} color={theme.colors.icon} />
            <SettingText>{t("email_notifications")}</SettingText>
            <Switch
              value={notifications.email}
              onValueChange={() => toggleNotification("email")}
              trackColor={{
                false: theme.colors.switchTrack,
                true: theme.colors.switchTrackActive,
              }}
              thumbColor={
                notifications.email
                  ? theme.colors.switchThumbActive
                  : theme.colors.switchThumb
              }
            />
          </SettingItem>
          <SettingItem>
            <Ionicons
              name="logo-whatsapp"
              size={24}
              color={theme.colors.icon}
            />
            <SettingText>{t("whatsapp_notifications")}</SettingText>
            <Switch
              value={notifications.whatsapp}
              onValueChange={() => toggleNotification("whatsapp")}
              trackColor={{
                false: theme.colors.switchTrack,
                true: theme.colors.switchTrackActive,
              }}
              thumbColor={
                notifications.whatsapp
                  ? theme.colors.switchThumbActive
                  : theme.colors.switchThumb
              }
            />
          </SettingItem>
        </Section>

        {/* Logout Section */}
        <Section>
          <SettingItem onPress={handleLogout}>
            <Ionicons
              name="log-out-outline"
              size={24}
              color={theme.colors.error}
            />
            <SettingText style={{ color: theme.colors.error }}>
              {t("logout.title")}
            </SettingText>
            <Ionicons
              name="chevron-forward"
              size={24}
              color={theme.colors.iconSecondary}
            />
          </SettingItem>
        </Section>
      </Content>

      {/* Language Selection Modal */}
      <ModalOverlay
        visible={showLanguageModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowLanguageModal(false)}
      >
        <ModalContent>
          <Pressable
            style={{ flex: 1 }}
            onPress={() => setShowLanguageModal(false)}
          />
          <ModalInnerContent>
            <ModalTitle>{t("language_selection")}</ModalTitle>
            {LANGUAGES.map((language) => (
              <LanguageOption
                key={language.code}
                onPress={() => changeLanguage(language.code)}
                style={
                  i18n.language === language.code && {
                    backgroundColor: theme.colors.selectedItemBg,
                  }
                }
              >
                <LanguageText
                  style={
                    i18n.language === language.code && {
                      color: theme.colors.primary,
                      fontWeight: "600",
                    }
                  }
                >
                  {t(language.name)}
                </LanguageText>
                {i18n.language === language.code && (
                  <Ionicons
                    name="checkmark"
                    size={24}
                    color={theme.colors.primary}
                  />
                )}
              </LanguageOption>
            ))}
            <CloseButton onPress={() => setShowLanguageModal(false)}>
              <CloseButtonText>{t("close")}</CloseButtonText>
            </CloseButton>
          </ModalInnerContent>
        </ModalContent>
      </ModalOverlay>

      {/* Logout Confirmation Modal */}
      <ConfirmationModal
        visible={showLogoutModal}
        title={t("logout.title")}
        message={t("logout.message")}
        variant="danger"
        icon="log-out-outline"
        confirmText={t("logout.confirm")}
        cancelText={t("logout.cancel")}
        onCancel={() => setShowLogoutModal(false)}
        onConfirm={confirmLogout}
        loading={isLoggingOut}
      />
    </Container>
  );
}
